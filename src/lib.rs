use pyo3::prelude::*;
use pyo3::types::PyDict;
use serde_json::Value;
use std::collections::HashMap;

mod exifier;
use exifier::{Exifier, EXIGrammar};

/// Python wrapper for the Exifier struct
#[pyclass]
struct PyExifier {
    inner: Exifier,
}

#[pymethods]
impl PyExifier {
    #[new]
    fn new() -> Self {
        PyExifier {
            inner: Exifier::default(),
        }
    }

    /// Load grammar from a JSON string
    fn load_grammar(&self, grammar_json: &str) -> PyResult<()> {
        let exi_grammar = EXIGrammar::from_json(grammar_json)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Failed to parse grammar: {}", e)))?;

        self.inner.load_grammar(exi_grammar)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Failed to load grammar: {:?}", e)))?;

        Ok(())
    }

    /// Encode a Python dict/object to bytes
    fn encode(&self, json_data: &PyAny) -> PyResult<Vec<u8>> {
        // Convert Python object to JSON string first
        let json_string = if let Ok(dict) = json_data.downcast::<PyDict>() {
            // Convert PyDict to JSON string
            pythonize::depythonize(dict)
                .and_then(|value: Value| serde_json::to_string(&value))
                .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Failed to serialize Python object: {}", e)))?
        } else if let Ok(string) = json_data.extract::<String>() {
            string
        } else {
            return Err(PyErr::new::<pyo3::exceptions::PyTypeError, _>("Expected dict or string"));
        };

        // Parse JSON string to Value
        let json_value: Value = serde_json::from_str(&json_string)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid JSON: {}", e)))?;

        // Encode using the inner exifier
        self.inner.encode(json_value)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Encoding failed: {:?}", e)))
    }

    /// Decode bytes to a Python dict
    fn decode(&self, encoded_bytes: Vec<u8>) -> PyResult<PyObject> {
        let json_value = self.inner.decode(encoded_bytes)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Decoding failed: {:?}", e)))?;

        // Convert JSON Value to Python object
        Python::with_gil(|py| {
            pythonize::pythonize(py, &json_value)
                .map_err(|e| PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Failed to convert to Python object: {}", e)))
        })
    }
}

/// Python module definition
#[pymodule]
fn exify(_py: Python, m: &PyModule) -> PyResult<()> {
    m.add_class::<PyExifier>()?;
    Ok(())
}