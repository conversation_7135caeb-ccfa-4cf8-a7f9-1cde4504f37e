use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Main GRS (Grammar) structure that represents the entire grammar file
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GrsFile {
    pub qnames: QNames,
    #[serde(rename = "simpleDatatypes")]
    pub simple_datatypes: Vec<SimpleDatatype>,
    pub grs: Grs,
}

/// QNames section containing namespace and local name mappings
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct QNames {
    #[serde(rename = "namespaceContext")]
    pub namespace_context: Vec<NamespaceContext>,
}

/// Namespace context with URI and QName mappings
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct NamespaceContext {
    #[serde(rename = "uriID")]
    pub uri_id: u32,
    pub uri: String,
    #[serde(rename = "qnameContext")]
    pub qname_context: Vec<QNameContext>,
}

/// QName context with local name mappings
#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct QNameContext {
    #[serde(rename = "uriID")]
    pub uri_id: u32,
    #[serde(rename = "localNameID")]
    pub local_name_id: u32,
    #[serde(rename = "localName")]
    pub local_name: String,
    #[serde(rename = "globalElementGrammarID")]
    pub global_element_grammar_id: Option<u32>,
    #[serde(rename = "globalTypeGrammarID")]
    pub global_type_grammar_id: Option<u32>,
}

/// Simple datatype definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimpleDatatype {
    #[serde(rename = "simpleDatatypeID")]
    pub simple_datatype_id: u32,
    #[serde(rename = "type")]
    pub datatype: String,
    #[serde(rename = "listType")]
    pub list_type: Option<String>,
    #[serde(rename = "lowerBound")]
    pub lower_bound: Option<i32>,
    #[serde(rename = "upperBound")]
    pub upper_bound: Option<i32>,
    #[serde(rename = "datetimeType")]
    pub datetime_type: Option<String>,
}

/// Main GRS section containing grammar rules
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Grs {
    #[serde(rename = "documentGrammarID")]
    pub document_grammar_id: u32,
    #[serde(rename = "fragmentGrammarID")]
    pub fragment_grammar_id: u32,
    pub grammar: Vec<Grammar>,
}

/// Individual grammar rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Grammar {
    #[serde(rename = "grammarID")]
    pub grammar_id: String,
    #[serde(rename = "type")]
    pub grammar_type: String,
    #[serde(rename = "isTypeCastable")]
    pub is_type_castable: Option<bool>,
    #[serde(rename = "isNillable")]
    pub is_nillable: Option<bool>,
    pub production: Vec<Production>,
}

/// Production rule within a grammar
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Production {
    pub event: String,
    #[serde(rename = "nextGrammarID")]
    pub next_grammar_id: Option<i32>,
    #[serde(rename = "startElementNamespaceID")]
    pub start_element_namespace_id: Option<u32>,
    #[serde(rename = "startElementLocalNameID")]
    pub start_element_local_name_id: Option<u32>,
    #[serde(rename = "startElementGrammarID")]
    pub start_element_grammar_id: Option<u32>,
    #[serde(rename = "charactersDatatypeID")]
    pub characters_datatype_id: Option<u32>,
}

impl GrsFile {
    /// Load a GRS file from a JSON string
    pub fn from_json(json_content: &str) -> Result<Self, serde_json::Error> {
        serde_json::from_str(json_content)
    }

    /// Load a GRS file from a file path
    pub fn from_file(file_path: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let content = std::fs::read_to_string(file_path)?;
        let grs_file = Self::from_json(&content)?;
        Ok(grs_file)
    }

    /// Get a local name by URI ID and local name ID
    pub fn get_local_name(&self, uri_id: u32, local_name_id: u32) -> Option<&str> {
        self.qnames
            .namespace_context
            .iter()
            .find(|ns| ns.uri_id == uri_id)?
            .qname_context
            .iter()
            .find(|qname| qname.local_name_id == local_name_id)
            .map(|qname| qname.local_name.as_str())
    }

    /// Get a simple datatype by ID
    pub fn get_simple_datatype(&self, datatype_id: u32) -> Option<&SimpleDatatype> {
        self.simple_datatypes
            .iter()
            .find(|dt| dt.simple_datatype_id == datatype_id)
    }

    /// Get a grammar by ID
    pub fn get_grammar(&self, grammar_id: &str) -> Option<&Grammar> {
        self.grs
            .grammar
            .iter()
            .find(|g| g.grammar_id == grammar_id)
    }

    /// Get the document grammar (starting point for document parsing)
    pub fn get_document_grammar(&self) -> Option<&Grammar> {
        self.get_grammar(&self.grs.document_grammar_id.to_string())
    }

    /// Get the fragment grammar (starting point for fragment parsing)
    pub fn get_fragment_grammar(&self) -> Option<&Grammar> {
        self.get_grammar(&self.grs.fragment_grammar_id.to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_grs_file_parsing() {
        let json_content = r#"
        {
            "qnames": {
                "namespaceContext": [
                    {
                        "uriID": 0,
                        "uri": "",
                        "qnameContext": [
                            {
                                "uriID": 0,
                                "localNameID": 0,
                                "localName": "test"
                            }
                        ]
                    }
                ]
            },
            "simpleDatatypes": [
                {
                    "simpleDatatypeID": 0,
                    "type": "STRING"
                }
            ],
            "grs": {
                "documentGrammarID": 0,
                "fragmentGrammarID": 1,
                "grammar": [
                    {
                        "grammarID": "0",
                        "type": "document",
                        "production": [
                            {
                                "event": "startDocument",
                                "nextGrammarID": 1
                            }
                        ]
                    }
                ]
            }
        }
        "#;

        let grs_file = GrsFile::from_json(json_content).unwrap();
        assert_eq!(grs_file.qnames.namespace_context.len(), 1);
        assert_eq!(grs_file.simple_datatypes.len(), 1);
        assert_eq!(grs_file.grs.grammar.len(), 1);
    }
}