use crate::exifier::prelude::*;
use crate::exifier::grammar::EXIGrammar;
use quick_xml::events::Event;
use quick_xml::reader::Reader;
use tracing::instrument;
use xml2json_rs::{
    Declaration, Encoding, Indentation, JsonBuilder, JsonConfig, Version, XmlBuilder, XmlConfig,
};

use std::error::Error;

#[derive(Debug)]
pub struct Exifier {
    grammar: String,
    xml: String,
}

impl Exifier {
    pub fn default() -> Self {
        Exifier {
            grammar: String::new(),
            xml: String::new(),
        }
    }

    pub fn load_grammar(&self, exi_grammar: EXIGrammar) -> Result<()> {
        tracing::debug!("Loading EXI grammar with {} grammar rules", exi_grammar.grs.grammar.len());
        tracing::debug!("Document grammar ID: {}", exi_grammar.grs.document_grammar_id);
        tracing::debug!("Fragment grammar ID: {}", exi_grammar.grs.fragment_grammar_id);
        // TODO: Store the grammar for use in encoding/decoding
        Ok(())
    }

    pub fn encode(&self, json_string: String) -> Result<Vec<u8>> {
        tracing::debug!("Encode.");
        let mut xml_builder = XmlConfig::new()
            .decl(Declaration::new(
                Version::XML10,
                Some(Encoding::UTF8),
                Some(false),
            ))
            .finalize();
        let xml = xml_builder.build_from_json_string(&json_string).unwrap();
        tracing::debug!("{:}", xml);
        self.parse_xml(&xml);
        let json_builder = JsonBuilder::default();
        let json = json_builder.build_string_from_xml(&xml).unwrap();
        println!("{:?}", json);

        // Return the incoming JSON string as a vector of bytes
        Ok(json_string.into_bytes())
    }

    pub fn decode(&self, encoded_bytes: Vec<u8>) -> Result<String> {
        // Simply decode the incoming bytes to a string and return
        Ok(String::from_utf8(encoded_bytes).unwrap())
    }

    pub fn parse_xml(&self, xml: &str) {
        let mut reader = Reader::from_str(xml);
        let mut buf = Vec::new();
        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(e)) => tracing::debug!("Start element: {:?}", e.name()),
                Ok(Event::Text(e)) => tracing::debug!("Text: {:?}", e.unescape().unwrap()),
                Ok(Event::End(e)) => tracing::debug!("End element: {:?}", e.name()),
                Ok(Event::Eof) => break,
                Err(e) => panic!("Error: {:?}", e),
                _ => (),
            }
            buf.clear();
        }
    }
}
