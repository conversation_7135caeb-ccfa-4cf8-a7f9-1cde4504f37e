#![allow(unused)]
use std::fs;
use std::path::PathBuf;

use clap::Parser;
use serde_json::Value;

// To be removed later.
use tracing_subscriber;
mod exifier;

use exifier::{Error, Exifier, GrsFile};

/// Compare two JSON strings semantically, ignoring element order
fn compare_json_strings(json1: &str, json2: &str) -> Result<bool, String> {
    // Parse both JSON strings
    let value1: Value =
        serde_json::from_str(json1).map_err(|e| format!("Failed to parse first JSON: {}", e))?;

    let value2: Value =
        serde_json::from_str(json2).map_err(|e| format!("Failed to parse second JSON: {}", e))?;

    // Compare the parsed values (this handles order independence)
    Ok(value1 == value2)
}

#[derive(Parser, Debug)]
#[command(version, about, long_about = None)]
struct Args {
    /// Name of the person to greet
    #[arg(
        short,
        long,
        help = "Json to encode to exi",
        default_value = r#"{"lastname": "Smith","age": 30}"#
    )]
    json: String,
    #[arg(
        short,
        long,
        help = "Path to grammar file",
        default_value = r#"./../../resources/examples/example_1/test_schema.xsd.grs"#
    )]
    grammar: String,
    #[arg(short, long, help = "Exi to decode", default_value = r#""#)]
    exi: String,
}

fn main() {
    tracing_subscriber::fmt::init();
    let args = Args::parse();
    let json_to_encode = args.json;
    tracing::info!("Json to encode:\n {json_to_encode}");

    // Read grammar content from file path
    let grammar_path = args.grammar;
    tracing::info!("Grammar file path: {grammar_path}");
    let grammar_content = fs::read_to_string(&grammar_path).expect(&format!(
        "Error reading grammar file from path: {}",
        grammar_path
    ));
    tracing::info!("Grammar content loaded from file.");

    // Parse the GRS file
    let grs_file = GrsFile::from_json(&grammar_content)
        .expect("Failed to parse GRS file");
    tracing::info!("GRS file parsed successfully");
    tracing::debug!("Document grammar ID: {}", grs_file.grs.document_grammar_id);
    tracing::debug!("Fragment grammar ID: {}", grs_file.grs.fragment_grammar_id);

    let exifier = Exifier::default();
    exifier.load_grammar(grammar_content);

    let encoded_bytes = exifier.encode(json_to_encode.clone()).unwrap();
    let decoded_json = exifier.decode(encoded_bytes).unwrap();

    tracing::info!("Decoded exi:\n {decoded_json}");

    // Compare the original and decoded JSON
    let comparison_result = compare_json_strings(&json_to_encode, &decoded_json);
    match comparison_result {
        Ok(are_equal) => {
            if are_equal {
                tracing::info!(
                    "✅ JSON comparison: Original and decoded JSON are semantically identical"
                );
            } else {
                tracing::warn!("❌ JSON comparison: Original and decoded JSON are different");
            }
        }
        Err(e) => {
            tracing::error!("❌ JSON comparison failed: {}", e);
        }
    }
}

fn load_grammar_from_file() -> String {
    fs::read_to_string("/Users/<USER>/Desktop/Personal/repos/exify-codec/resources/examples/example_1/test_schema.json").expect("Error reading example json from disk.")
}
