#!/usr/bin/env python3
"""
Example usage of the EXI codec Python bindings.
"""

import json
from exify import PyExifier

def main():
    # Create an exifier instance
    exifier = PyExifier()
    
    # Load grammar from file
    try:
        with open("resources/examples/example_1/test_schema.xsd.grs", "r") as f:
            grammar_content = f.read()
        
        print("Loading grammar...")
        exifier.load_grammar(grammar_content)
        print("Grammar loaded successfully!")
        
    except Exception as e:
        print(f"Failed to load grammar: {e}")
        return
    
    # Test data
    test_data = {
        "lastname": "Smith",
        "age": 30
    }
    
    print(f"Original data: {test_data}")
    
    try:
        # Encode the data
        print("Encoding...")
        encoded_bytes = exifier.encode(test_data)
        print(f"Encoded to {len(encoded_bytes)} bytes")
        
        # Decode the data
        print("Decoding...")
        decoded_data = exifier.decode(encoded_bytes)
        print(f"Decoded data: {decoded_data}")
        
        # Compare original and decoded
        if test_data == decoded_data:
            print("✅ Success: Original and decoded data match!")
        else:
            print("❌ Error: Original and decoded data don't match!")
            print(f"Original: {test_data}")
            print(f"Decoded:  {decoded_data}")
            
    except Exception as e:
        print(f"Error during encoding/decoding: {e}")

if __name__ == "__main__":
    main()
